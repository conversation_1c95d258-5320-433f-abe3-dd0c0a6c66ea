<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Word Generator</title>
</head>
<body>
    <h1>Test Word Generator</h1>
    <button onclick="testWordGenerator()">Test Generate Contract</button>
    <div id="result"></div>

    <script>
        // Mock data for testing
        const mockContract = {
            id: "test-id",
            name: "Test Contract",
            version: "1.0",
            dealId: "deal-123",
            quotationId: "quote-123",
            contractNumber: "HDKT-ASIC-2025-001",
            signDate: "2025-01-15",
            isFinal: true,
            buyer: {
                customerId: "customer-123",
                customerName: "CÔNG TY ABC",
                representative: "NGUYỄN VĂN A",
                position: "Giám đốc"
            },
            seller: {
                bankName: "Vietcombank",
                bankBranch: "Chi nhánh Hà Nội",
                bankAccount: "**********",
                staffId: "staff-123",
                staffName: "TRẦN THỊ B",
                position: "Trưởng phòng kinh doanh",
                positionId: "pos-123"
            },
            payments: [
                {
                    paymentType: 1,
                    valuePercent: 40,
                    daysAfterSign: 10,
                    paymentDocumentType: 1,
                    paymentDocumentCount: 1
                },
                {
                    paymentType: 1,
                    valuePercent: 60,
                    daysAfterSign: 10,
                    paymentDocumentType: 1,
                    paymentDocumentCount: 1
                }
            ],
            delivery: {
                deliveryType: 1,
                deliveryWeek: 12,
                regionDeliveryType: 1,
                country: "Việt Nam",
                city: "Thành phố Hồ Chí Minh",
                district: "Thành phố Thủ Đức",
                ward: "Phường Thảo Điền",
                addressDetail: "11 Tống Hữu Định",
                documentIncludedType: 1,
                documentQuantity: 1
            }
        };

        const mockCustomer = {
            id: "customer-123",
            name: "CÔNG TY ABC",
            taxCode: "0123456789"
        };

        function testWordGenerator() {
            // Simulate the formatContractToHTML function
            const result = generateTestHTML(mockContract, mockCustomer);
            
            // Open in new window
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.write(result);
                newWindow.document.close();
            }
        }

        function formatVietnameseDate(dateString) {
            if (!dateString) {
                return '___';
            }

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    return '___';
                }

                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();

                return `ngày ${day} tháng ${month} năm ${year}`;
            } catch {
                return '___';
            }
        }

        function generateTestHTML(contract, dataCustomer) {
            return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Hợp đồng kinh tế</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            font-size: 15px;
            line-height: 1.4;
            margin: 0;
            padding: 40px;
            background-color: #e0e0e0;
            color: #000;
            min-height: 100vh;
        }
        .a4-container {
            width: 210mm;
            min-height: 297mm;
            background-color: #ffffff;
            margin: 0 auto 40px auto;
            padding: 20mm;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }
        .terms-title {
            font-weight: bold;
            text-decoration: underline;
            margin: 20px 0 10px 0;
        }
        .section {
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="a4-container">
        <div class="terms-title">ĐIỀU 3: HÌNH THỨC THANH TOÁN</div>
        <div class="section">
            3.1 Tổng giá trị hợp đồng: $(tổng giá trị hợp đồng) VNĐ (Đã bao gồm thuế VAT)<br>
            <em>(Bằng chữ: $(tổng giá trị hợp đồng bằng chữ))</em><br>
            3.2 Hình thức thanh toán: Bằng chuyển khoản.
        </div>

        <div class="section" style="margin-top: 20px;">
            3.3 Thanh toán: ${contract.payments.length} đợt:<br><br>

            ${contract.payments
                .map((payment, index) => {
                    const dotNumber = index + 1;
                    const paymentTypeText =
                        payment.paymentType === 1 ? 'tạm ứng' : 'thanh toán';
                    const timeDescription =
                        dotNumber === 1
                            ? `trong vòng $(${payment.daysAfterSign}) ngày sau khi ký hợp đồng`
                            : dotNumber === 2
                            ? `trong vòng $(${payment.daysAfterSign}) ngày kể từ khi bên B gửi Bên A thông báo giao hàng và trước khi giao hàng`
                            : `trong vòng $(${payment.daysAfterSign}) ngày`;

                    return `
            <strong>Đợt ${dotNumber}:</strong> Bên A $(${paymentTypeText}) ${
                        dotNumber > 1 ? 'tiếp ' : ''
                    }cho Bên B $(${
                        payment.valuePercent
                    })% giá trị hợp đồng ${timeDescription} với số tiền thanh toán là: ………………… VNĐ <em>(Bằng chữ: …………………………../)</em>.<br><br>

            Hồ sơ đề nghị ${paymentTypeText} gồm:<br>
            + Giấy đề nghị ${paymentTypeText} của Bên B: 01 bản gốc<br>
            + Bảo lãnh ${paymentTypeText} của ngân hàng (có giá trị bằng 100% giá trị ${paymentTypeText} Đợt ${dotNumber}): 01 bản gốc<br><br>

            Bảo lãnh ${paymentTypeText} có hiệu lực kể từ ngày Bên B nhận được tiền ${paymentTypeText} đợt ${dotNumber} theo Hợp đồng đến ngày hai bên ký Biên bản bàn giao Hàng Hóa.${
                        index < contract.payments.length - 1 ? '<br><br>' : ''
                    }`;
                })
                .join('')}
        </div>

        <div class="terms-title">ĐIỀU 4: CUNG CẤP HÀNG HÓA VÀ CÁC TÀI LIỆU KÈM THEO</div>
        <div class="section">
            4.1 Thời gian giao hàng dự kiến: Trong vòng $(${contract.delivery.deliveryWeek}) tuần kể từ ngày nhận được tiền tạm ứng của bên A<br><br>

            4.2 Địa chỉ giao hàng: $(${contract.delivery.addressDetail}, ${contract.delivery.ward}, ${contract.delivery.district}, ${contract.delivery.city}, ${contract.delivery.country})<br><br>

            4.3 Tài liệu đi kèm khi giao hàng gồm:
            <ul>
                <li>Hóa đơn GTGT: $(${contract.delivery.documentQuantity}) bản điện tử</li>
                <li>Biên bản bàn giao hàng: $(${contract.delivery.documentQuantity}) bản gốc</li>
                <li>Trong vòng 07 ngày kể từ ngày giao hàng, sẽ bàn giao:</li>
                <li>Chứng chỉ xuất xứ do Hãng sản xuất cấp (Certificate of Origin): $(${contract.delivery.documentQuantity}) bản gốc</li>
                <li>Chứng chỉ chất lượng hàng hoá do Hãng sản xuất cấp (Certificate of Quality): $(${contract.delivery.documentQuantity}) bản gốc</li>
            </ul>
        </div>
    </div>
</body>
</html>`;
        }
    </script>
</body>
</html>
