'use client';
import { useDeleteProduct, useSearchProduct } from '@/apis/product/product.api';
import {
    ResponseSearchProduct,
    SearchProduct,
} from '@/apis/product/product.type';
import ButtonHeader from '@/components/common/ButtonHeader';
import ComboboxSelectUserControl from '@/components/common/FormController/ComboboxSelectUserControl';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import { ROUTES } from '@/lib/routes';
import { getOneMonthAgo, getToday } from '@/utils/time';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    <PERSON><PERSON>,
    <PERSON>,
    CardHeader,
    Col,
    Container,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Row,
} from 'reactstrap';
import useGetColumn from './_hook/useGetColumn';
import { ACTIONS } from './_types/action.type';
import { showToastSuccess } from '@/utils/toast-message';
const MantineTable = dynamic(
    () =>
        import('@/components/common/MantineReactTable').then((mod) => ({
            default: mod.default as typeof mod.default<ResponseSearchProduct>,
        })),
    {
        ssr: false,
    },
);
const Products = () => {
    const router = useRouter();
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedNames, setSelectedNames] = useState<string[]>([]);
    const [modal, setModal] = useState(false);

    const methods = useForm<SearchProduct>({
        defaultValues: {
            Page: 1,
            PageSize: 10,
            IsDeleted: false,
            FromDate: getOneMonthAgo(),
            ToDate: getToday(),
        },
    });
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const { control, setValue } = methods;
    const [Name, UserNameCreated, Page, PageSize, IsDeleted, FromDate, ToDate] =
        useWatch({
            control,
            name: [
                'Name',
                'UserNameCreated',
                'Page',
                'PageSize',
                'IsDeleted',
                'FromDate',
                'ToDate',
            ],
        });

    const handleSelectedAction = (
        action: ACTIONS,
        row?: ResponseSearchProduct,
    ) => {
        if (!row) {
            return;
        }
        switch (action) {
            case ACTIONS.DELETE:
                setSelectedIds([row.id]);
                setSelectedNames([row.name]);
                setModal(true);
                break;
            case ACTIONS.EDIT:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.UPDATE.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            case ACTIONS.VIEW_DETAIL:
                router.push(
                    ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.DETAIL.replace(
                        ':id',
                        row.id,
                    ),
                );
                break;
            default:
                console.error('Action not found');
                break;
        }
    };

    const columns = useGetColumn({
        onSelectedAction: handleSelectedAction,
        page: 'list',
    });
    const { data, refetch, isLoading } = useSearchProduct({
        Name,
        UserNameCreated,
        FromDate,
        ToDate,
        Page,
        PageSize,
        IsDeleted,
    });
    const { items: listProduct = [], totalItems } = data ?? {};
    const handleClose = () => {
        setModal(false);
        setSelectedIds([]);
        setSelectedNames([]);
    };
    const { mutate: deleteProduct } = useDeleteProduct({
        onSuccess: () => {
            showToastSuccess({
                title: 'Xóa sản phẩm thành công',
                message:
                    'Thông tin sản phẩm đã được xóa thành công trong hệ thống.',
            });
            setModal(false);
            setSelectedIds([]);
            setSelectedNames([]);
            refetch();
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const handleConfimDelete = () => {
        deleteProduct({ ids: selectedIds, isDeleted: false });
    };
    const handleDelete = () => {
        setModal(true);
    };
    return (
        <FormProvider {...methods}>
            <Container fluid>
                <Col md={12}>
                    <ButtonHeader
                        onCreateNew={() =>
                            router.push(
                                ROUTES.PRODUCT_MANAGEMENT.PRODUCTS.CREATE,
                            )
                        }
                        showDateFilters={true}
                    />
                </Col>

                <Col md={12}>
                    <Card>
                        <CardHeader>
                            <Row>
                                <Col lg={9}>
                                    <div className='d-flex gap-3'>
                                        <InputSearchNameWithApiControl
                                            name='Name'
                                            placeholder='Tìm kiếm theo tên sản phẩm'
                                        />

                                        <ComboboxSelectUserControl
                                            name='UserNameCreated'
                                            placeholder='Người tạo'
                                            style={{ width: '250px' }}
                                        />
                                    </div>
                                </Col>
                                <Col lg={3}>
                                    <div className='d-flex justify-content-end gap-2'>
                                        {selectedIds.length > 0 && (
                                            <Button
                                                style={{
                                                    backgroundColor: 'red',
                                                    border: 'none',
                                                    color: 'white',
                                                }}
                                                onClick={() => handleDelete()}
                                            >
                                                Xóa
                                            </Button>
                                        )}
                                        <Button
                                            outline
                                            className='filter-button'
                                            style={{
                                                border: 'none',
                                                backgroundColor: '#dff0fa',
                                            }}
                                        >
                                            <i className='ri-filter-line text-primary'></i>
                                        </Button>
                                        <Dropdown
                                            isOpen={dropdownOpen}
                                            toggle={toggleDropdown}
                                            direction='down'
                                        >
                                            <DropdownToggle
                                                outline
                                                className='settings-button'
                                                style={{
                                                    border: 'none',
                                                    backgroundColor: '#dff0fa',
                                                }}
                                            >
                                                <i className='ri-settings-2-line text-info'></i>
                                            </DropdownToggle>
                                            <DropdownMenu>
                                                <DropdownItem
                                                    onClick={() =>
                                                        router.push(
                                                            ROUTES
                                                                .PRODUCT_MANAGEMENT
                                                                .PRODUCTS
                                                                .RESTORE,
                                                        )
                                                    }
                                                >
                                                    Khôi phục tài khoản
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </Dropdown>
                                    </div>
                                </Col>
                            </Row>
                        </CardHeader>
                        <MantineTable
                            columns={columns}
                            data={listProduct}
                            isLoading={isLoading}
                            totalItems={Number(totalItems)}
                            onPageChange={(page: number) => {
                                setValue('Page', page);
                            }}
                            onPageSizeChange={(size: number) => {
                                setValue('PageSize', size);
                            }}
                            tableProps={{
                                mantineSelectAllCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },
                                mantineSelectCheckboxProps: {
                                    size: 'xs',
                                    color: '#0ab39c',
                                    style: {
                                        cursor: 'pointer',
                                        visibility: 'visible',
                                        display: 'inline-flex',
                                    },
                                },

                                state: {
                                    rowSelection: selectedIds.reduce(
                                        (acc, id) => {
                                            const index = listProduct.findIndex(
                                                (
                                                    contact: ResponseSearchProduct,
                                                ) => contact.id === id,
                                            );
                                            if (index !== -1) {
                                                acc[index] = true;
                                            }
                                            return acc;
                                        },
                                        {} as Record<string, boolean>,
                                    ),
                                    pagination: {
                                        pageIndex: (Page ? Page : 1) - 1,
                                        pageSize: PageSize ? PageSize : 10,
                                    },
                                },

                                onRowSelectionChange: (updater) => {
                                    let selectedRows: Record<string, boolean>;
                                    if (typeof updater === 'function') {
                                        const currentSelection =
                                            selectedIds.reduce(
                                                (acc, id) => {
                                                    const index =
                                                        listProduct.findIndex(
                                                            (
                                                                contact: ResponseSearchProduct,
                                                            ) =>
                                                                contact.id ===
                                                                id,
                                                        );
                                                    if (index !== -1) {
                                                        acc[index] = true;
                                                    }
                                                    return acc;
                                                },
                                                {} as Record<string, boolean>,
                                            );
                                        selectedRows =
                                            updater(currentSelection);
                                    } else {
                                        selectedRows = updater;
                                    }

                                    const newSelectedIds: string[] = [];
                                    const newSelectedNames: string[] = [];
                                    Object.keys(selectedRows)
                                        .filter((key) => selectedRows[key])
                                        .forEach((key) => {
                                            const item =
                                                listProduct[parseInt(key)];
                                            if (item) {
                                                newSelectedIds.push(item.id);
                                                newSelectedNames.push(
                                                    item.name,
                                                );
                                            }
                                        });

                                    setSelectedIds(newSelectedIds);
                                    setSelectedNames(newSelectedNames);
                                },
                                mantineTableBodyCellProps: {
                                    align: 'left',
                                },
                            }}
                        />
                    </Card>
                </Col>
            </Container>
            <ModalDelete
                onDelete={handleConfimDelete}
                onClose={handleClose}
                isOpen={modal}
                page='sản phẩm'
                data={selectedNames}
            />
        </FormProvider>
    );
};

export default Products;
