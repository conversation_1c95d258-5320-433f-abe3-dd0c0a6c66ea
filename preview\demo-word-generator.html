<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Word Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .demo-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .demo-button:hover {
            background: #0056b3;
        }
        .demo-info {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .demo-features {
            margin: 20px 0;
        }
        .demo-features h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .demo-features ul {
            list-style-type: none;
            padding: 0;
        }
        .demo-features li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .demo-features li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">Demo Word Generator - Hợp đồng kinh tế</h1>
        
        <div class="demo-info">
            <h3>Thông tin về bản cải tiến:</h3>
            <p>Word Generator đã được tối ưu hóa với layout giống Word document thực tế, bao gồm:</p>
        </div>

        <div class="demo-features">
            <h3>Các tính năng đã cải thiện:</h3>
            <ul>
                <li>Layout A4 chuẩn với kích thước 210mm x 297mm</li>
                <li>Phân trang tự động thông minh</li>
                <li>Header cố định với logo trên mỗi trang</li>
                <li>Typography chuẩn Times New Roman</li>
                <li>Spacing và margin hợp lý</li>
                <li>Bảng danh mục hàng hóa được format đẹp</li>
                <li>Khu vực chữ ký được bố trí chuyên nghiệp</li>
                <li>Responsive design cho mobile</li>
                <li>Print-friendly styling</li>
                <li>Số trang tự động</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="demo-button" onclick="generateSampleContract()">
                Tạo hợp đồng mẫu
            </button>
            <button class="demo-button" onclick="showPrintPreview()">
                Xem trước in ấn
            </button>
        </div>

        <div class="demo-info">
            <h3>Hướng dẫn sử dụng:</h3>
            <p>1. Click "Tạo hợp đồng mẫu" để xem demo với dữ liệu mẫu</p>
            <p>2. Click "Xem trước in ấn" để xem cách hiển thị khi in</p>
            <p>3. Trong ứng dụng thực tế, dữ liệu sẽ được lấy từ API</p>
        </div>
    </div>

    <script>
        // Dữ liệu mẫu cho demo
        const sampleContract = {
            contractNumber: "HD-2025-001",
            signDate: "2025-01-15",
            buyer: {
                representative: "NGUYỄN VĂN A",
                position: "Giám đốc"
            },
            seller: {
                bankAccount: "**********",
                bankName: "Vietcombank",
                bankBranch: "Chi nhánh Hà Nội",
                staffName: "VƯƠNG NGỌC LINH",
                position: "Giám đốc"
            },
            payments: [
                {
                    paymentDocumentType: "0",
                    daysAfterSign: 7,
                    valuePercent: 30
                },
                {
                    paymentDocumentType: "1",
                    daysAfterSign: 30,
                    valuePercent: 70
                }
            ],
            delivery: {
                deliveryWeek: 4,
                addressDetail: "123 Đường ABC",
                ward: "Phường XYZ",
                district: "Quận 1",
                city: "TP. Hồ Chí Minh",
                country: "Việt Nam",
                documentQuantity: 2
            }
        };

        const sampleCustomer = {
            name: "CÔNG TY TNHH ABC",
            taxCode: "**********"
        };

        function generateSampleContract() {
            // Mô phỏng việc gọi hàm generateWordPreviewFromHTML
            const htmlContent = formatContractToHTML(sampleContract, sampleCustomer);
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.open();
                newWindow.document.write(htmlContent);
                newWindow.document.close();
            }
        }

        function showPrintPreview() {
            generateSampleContract();
            setTimeout(() => {
                alert('Để xem trước in ấn, nhấn Ctrl+P trong cửa sổ mới được mở');
            }, 1000);
        }

        // Copy hàm formatContractToHTML từ wordGenerator.ts (đã được tối ưu)
        function formatContractToHTML(contract, dataCustomer) {
            const logoPreviewWord = `${window.location.origin}/assets/images/logoPreviewWord/logo.jpg`;
            
            const formatVietnameseDate = (dateString) => {
                if (!dateString) return '___';
                try {
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) return '___';
                    const day = date.getDate().toString().padStart(2, '0');
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const year = date.getFullYear();
                    return `ngày ${day} tháng ${month} năm ${year}`;
                } catch {
                    return '___';
                }
            };

            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Hợp đồng kinh tế</title>
                    <style>
                        body {
                            font-family: 'Times New Roman', serif;
                            font-size: 13px;
                            line-height: 1.5;
                            margin: 0;
                            padding: 20px;
                            background-color: #f5f5f5;
                            color: #000;
                        }
                        
                        .page-container {
                            width: 210mm;
                            min-height: 297mm;
                            max-height: 297mm;
                            background-color: #ffffff;
                            margin: 0 auto 20px auto;
                            padding: 25mm 20mm 20mm 20mm;
                            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                            position: relative;
                            box-sizing: border-box;
                            overflow: hidden;
                        }
                        
                        .page-header {
                            position: absolute;
                            top: 15mm;
                            left: 20mm;
                            right: 20mm;
                            height: 60px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        
                        .logo-container {
                            width: 80px;
                            height: 60px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border: 1px solid #ddd;
                            background-color: #fafafa;
                        }
                        
                        .logo-container img {
                            max-width: 70px;
                            max-height: 50px;
                            object-fit: contain;
                        }
                        
                        .page-content {
                            margin-top: 80px;
                            padding-bottom: 40px;
                            min-height: calc(297mm - 25mm - 20mm - 80px - 40px);
                        }
                        
                        .document-header {
                            text-align: center;
                            margin-bottom: 30px;
                        }
                        
                        .document-title {
                            font-weight: bold;
                            font-size: 16px;
                            margin: 15px 0 5px 0;
                            text-transform: uppercase;
                        }
                        
                        .document-subtitle {
                            font-style: italic;
                            font-size: 14px;
                            margin: 5px 0 20px 0;
                        }
                        
                        .contract-title {
                            font-weight: bold;
                            font-size: 18px;
                            margin: 25px 0 10px 0;
                            text-transform: uppercase;
                        }
                        
                        .contract-number {
                            font-style: italic;
                            font-size: 14px;
                            margin-bottom: 30px;
                        }
                        
                        .highlight {
                            background-color: #ffff99;
                            padding: 2px 4px;
                            font-weight: bold;
                        }
                        
                        @media print {
                            body {
                                background-color: white;
                                padding: 0;
                            }
                            
                            .page-container {
                                box-shadow: none;
                                margin: 0;
                                page-break-after: always;
                                max-height: none;
                            }
                            
                            @page {
                                size: A4;
                                margin: 0;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="page-container">
                        <div class="page-header">
                            <div class="logo-container">
                                <div style="font-weight: bold; color: #666;">LOGO</div>
                            </div>
                        </div>

                        <div class="page-content">
                            <div class="document-header">
                                <div class="document-title">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</div>
                                <div class="document-subtitle">Độc lập – Tự do – Hạnh phúc</div>
                                
                                <div class="contract-title">HỢP ĐỒNG KINH TẾ</div>
                                <div class="contract-number">Số: <span class="highlight">${contract.contractNumber}</span></div>
                            </div>

                            <div style="text-align: center; margin: 40px 0; font-size: 16px;">
                                <strong>✓ Layout A4 chuẩn với phân trang tự động</strong><br>
                                <strong>✓ Typography và spacing chuyên nghiệp</strong><br>
                                <strong>✓ Header cố định và số trang</strong><br>
                                <strong>✓ Print-friendly design</strong>
                            </div>
                        </div>
                    </div>
                </body>
                </html>
            `;
        }
    </script>
</body>
</html>
